-------------------------------------------------------------------------------
Test set: com.gl.service.commercial.service.CommercialServiceTestFixed
-------------------------------------------------------------------------------
Tests run: 6, Failures: 5, Errors: 0, Skipped: 0, Time elapsed: 4.425 s <<< FAILURE! - in com.gl.service.commercial.service.CommercialServiceTestFixed
testList_QueryResultNull_ShouldReturnEmptyResult  Time elapsed: 2 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE (wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL) AND s.shop_name like ?  AND s.id = ?  AND (wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
-> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:90)

	at com.gl.service.commercial.service.CommercialServiceTestFixed.testList_QueryResultNull_ShouldReturnEmptyResult(CommercialServiceTestFixed.java:211)

testList_ExportType_ShouldReturnAllResults  Time elapsed: 0.044 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应为2 ==> expected: <2> but was: <0>
	at com.gl.service.commercial.service.CommercialServiceTestFixed.testList_ExportType_ShouldReturnAllResults(CommercialServiceTestFixed.java:235)

testExportList_NormalExport_ShouldGenerateExcelFile  Time elapsed: 2.35 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE (wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL) AND s.shop_name like ?  AND s.id = ?  AND (wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
-> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:90)

	at com.gl.service.commercial.service.CommercialServiceTestFixed.testExportList_NormalExport_ShouldGenerateExcelFile(CommercialServiceTestFixed.java:268)

testList_QueryResultEmpty_ShouldReturnEmptyResult  Time elapsed: 0.004 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select wu.id,wu.avatar,wu.nickname,wu.phone,wu.gender,wu.auth_time,group_concat(DISTINCT(s.shop_name)) shopNames,group_concat(DISTINCT(d.name)) deviceNames, count(DISTINCT(d.id)) deviceNum from dub_wechat_user wu left join dub_user_shop_ref usr on wu.id= usr.user_id left join dub_shop s on s.id=usr.shop_id left join dub_device d on d.user_id=wu.id WHERE (wu.h5_openid IS NOT NULL or wu.openid IS NOT NULL) AND s.shop_name like ?  AND s.id = ?  AND (wu.nickname like ? or wu.phone like ?)  AND wu.auth_time >= ?  AND wu.auth_time <= ?  AND s.id in (?, ?, ?)
GROUP BY wu.id ) t",
    class java.lang.Long,
    "%测试店铺%",
    1L,
    "%测试用户%",
    "%测试用户%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59",
    1L,
    2L,
    3L
);
-> at com.gl.service.commercial.service.CommercialService.list(CommercialService.java:90)

	at com.gl.service.commercial.service.CommercialServiceTestFixed.testList_QueryResultEmpty_ShouldReturnEmptyResult(CommercialServiceTestFixed.java:183)

testList_NormalQuery_ShouldReturnSuccessResult  Time elapsed: 0.016 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应为2 ==> expected: <2> but was: <0>
	at com.gl.service.commercial.service.CommercialServiceTestFixed.testList_NormalQuery_ShouldReturnSuccessResult(CommercialServiceTestFixed.java:124)

